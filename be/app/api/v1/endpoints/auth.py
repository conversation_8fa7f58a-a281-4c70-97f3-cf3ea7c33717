from datetime import timedelta
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.core.config import settings
from app.core.database import get_db
from app.core.security import create_access_token
from app.crud import user as crud_user
from app.schemas.user import User

router = APIRouter()


@router.post("/login")
def login_for_access_token(
    db: Session = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    user = crud_user.authenticate_user(
        db, email=form_data.username, password=form_data.password
    )
    if not user:
        # Check if user exists but password is wrong
        existing_user = crud_user.get_user_by_email(db, email=form_data.username)
        if existing_user:
            detail = "Incorrect password"
        else:
            detail = "User not found. Please register first."

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )
    elif not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": User.model_validate(user)
    }


@router.get("/debug/users")
def list_users_debug(db: Session = Depends(get_db)) -> Any:
    """
    Debug endpoint to list all users (remove in production)
    """
    users = crud_user.get_users(db, skip=0, limit=100)
    return {
        "total_users": len(users),
        "users": [{"id": u.id, "email": u.email, "username": u.username, "is_active": u.is_active} for u in users]
    }
