# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/cw_core
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=cw_core

# API
API_V1_STR=/api/v1
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Environment
ENVIRONMENT=development
DEBUG=True

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
