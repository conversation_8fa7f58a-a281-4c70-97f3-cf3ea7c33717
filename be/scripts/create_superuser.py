#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a superuser for the application.
Usage: python scripts/create_superuser.py
"""

import sys
import os
from getpass import getpass

# Add the parent directory to the path so we can import our app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.crud import user as crud_user
from app.schemas.user import UserCreate
from app.models.user import User


def create_superuser():
    """Create a superuser interactively."""
    print("Creating superuser...")
    
    # Get user input
    email = input("Email: ").strip()
    if not email:
        print("Email is required!")
        return
    
    username = input("Username: ").strip()
    if not username:
        print("Username is required!")
        return
    
    password = getpass("Password: ")
    if not password:
        print("Password is required!")
        return
    
    password_confirm = getpass("Password (again): ")
    if password != password_confirm:
        print("Passwords don't match!")
        return
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Check if user already exists
        existing_user = crud_user.get_user_by_email(db, email=email)
        if existing_user:
            print(f"User with email {email} already exists!")
            return
        
        existing_user = crud_user.get_user_by_username(db, username=username)
        if existing_user:
            print(f"User with username {username} already exists!")
            return
        
        # Create superuser
        user_data = UserCreate(
            email=email,
            username=username,
            password=password,
            is_active=True,
            is_superuser=True
        )
        
        user = crud_user.create_user(db, user=user_data)
        print(f"Superuser created successfully!")
        print(f"ID: {user.id}")
        print(f"Email: {user.email}")
        print(f"Username: {user.username}")
        
    except Exception as e:
        print(f"Error creating superuser: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    create_superuser()
