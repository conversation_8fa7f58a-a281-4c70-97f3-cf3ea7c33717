import pytest
from app.crud import user as crud_user
from app.schemas.user import UserCreate


def test_create_user(db_session):
    """Test creating a user."""
    user_data = UserCreate(
        email="<EMAIL>",
        username="testuser",
        password="testpassword123",
        is_active=True,
        is_superuser=False
    )
    
    user = crud_user.create_user(db_session, user=user_data)
    
    assert user.email == "<EMAIL>"
    assert user.username == "testuser"
    assert user.is_active is True
    assert user.is_superuser is False
    assert user.hashed_password != "testpassword123"  # Should be hashed


def test_get_user_by_email(db_session):
    """Test getting a user by email."""
    user_data = UserCreate(
        email="<EMAIL>",
        username="testuser",
        password="testpassword123"
    )
    
    created_user = crud_user.create_user(db_session, user=user_data)
    retrieved_user = crud_user.get_user_by_email(db_session, email="<EMAIL>")
    
    assert retrieved_user is not None
    assert retrieved_user.id == created_user.id
    assert retrieved_user.email == "<EMAIL>"


def test_authenticate_user(db_session):
    """Test user authentication."""
    user_data = UserCreate(
        email="<EMAIL>",
        username="testuser",
        password="testpassword123"
    )
    
    crud_user.create_user(db_session, user=user_data)
    
    # Test correct credentials
    authenticated_user = crud_user.authenticate_user(
        db_session, email="<EMAIL>", password="testpassword123"
    )
    assert authenticated_user is not None
    assert authenticated_user.email == "<EMAIL>"
    
    # Test incorrect password
    failed_auth = crud_user.authenticate_user(
        db_session, email="<EMAIL>", password="wrongpassword"
    )
    assert failed_auth is None
    
    # Test non-existent user
    failed_auth = crud_user.authenticate_user(
        db_session, email="<EMAIL>", password="testpassword123"
    )
    assert failed_auth is None


def test_create_user_endpoint(client):
    """Test the create user API endpoint."""
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "testpassword123",
        "is_active": True,
        "is_superuser": False
    }
    
    response = client.post("/api/v1/users/", json=user_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["username"] == "testuser"
    assert data["is_active"] is True
    assert data["is_superuser"] is False
    assert "hashed_password" not in data  # Should not be returned


def test_login_endpoint(client, db_session):
    """Test the login API endpoint."""
    # First create a user
    user_data = UserCreate(
        email="<EMAIL>",
        username="testuser",
        password="testpassword123"
    )
    crud_user.create_user(db_session, user=user_data)
    
    # Test login
    login_data = {
        "username": "<EMAIL>",  # FastAPI OAuth2 uses 'username' field for email
        "password": "testpassword123"
    }
    
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
    assert "user" in data
    assert data["user"]["email"] == "<EMAIL>"
