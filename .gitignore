# Dependencies
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.prod

# Next.js
.next/
out/
build/
dist/

# Database
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore

# Testing
coverage/
.coverage
.pytest_cache/
.tox/

# Temporary files
*.tmp
*.temp
.cache/

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup

# Python specific
*.egg-info/
.eggs/
.mypy_cache/
.dmypy.json
dmypy.json

# Frontend specific
.turbo/
.vercel/

# Backend specific
alembic/versions/*.py
!alembic/versions/001_initial_migration.py
