# 🔧 Troubleshooting Guide

## Common Issues and Solutions

### 1. Email Validator Error
**Error**: `ImportError: email-validator is not installed`

**Solution**: The requirements.txt has been updated to include email-validator. Rebuild the Docker image:
```bash
docker-compose down
docker-compose build --no-cache backend
docker-compose up -d
```

### 2. Docker Daemon Not Running
**Error**: `Cannot connect to the Docker daemon`

**Solution**: 
- **macOS**: Start Docker Desktop application
- **Windows**: Start Docker Desktop application  
- **Linux**: Start Docker service: `sudo systemctl start docker`

### 3. Port Already in Use
**Error**: `Port 3000/8000/5432 is already in use`

**Solution**: 
```bash
# Find and kill processes using the ports
lsof -ti:3000 | xargs kill -9
lsof -ti:8000 | xargs kill -9
lsof -ti:5432 | xargs kill -9

# Or change ports in docker-compose.yml
```

### 4. Database Connection Failed
**Error**: `could not translate host name "db" to address`

**Solution**: 
```bash
# Ensure database container is running
docker-compose ps

# Restart services in correct order
docker-compose down
docker-compose up -d db
sleep 10
docker-compose up -d backend frontend
```

### 5. Frontend Build Errors
**Error**: React 19 peer dependency issues

**Solution**: The setup already handles this with `--force` flag, but if issues persist:
```bash
cd fe
rm -rf node_modules package-lock.json
npm install --force
```

### 6. Backend Import Errors
**Error**: Module import errors in Python

**Solution**: 
```bash
# Rebuild backend container
docker-compose build --no-cache backend
docker-compose up -d backend
```

### 7. Migration Errors
**Error**: Alembic migration failures

**Solution**: 
```bash
# Reset migrations (development only)
docker-compose exec backend alembic downgrade base
docker-compose exec backend alembic upgrade head

# Or recreate database
docker-compose down -v
docker-compose up -d
make migrate
```

### 8. CORS Errors in Browser
**Error**: Cross-origin request blocked

**Solution**: Check backend CORS configuration in `be/.env`:
```env
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
```

### 9. JWT Token Issues
**Error**: Authentication failures

**Solution**: 
1. Check SECRET_KEY in backend `.env`
2. Clear browser localStorage
3. Restart backend service

### 10. Permission Denied Errors
**Error**: Permission denied when running scripts

**Solution**: 
```bash
chmod +x scripts/setup.sh
chmod +x scripts/*.sh
```

## Debug Commands

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs backend
docker-compose logs frontend
docker-compose logs db

# Follow logs in real-time
docker-compose logs -f backend
```

### Check Service Status
```bash
# List running containers
docker-compose ps

# Check container health
docker-compose exec backend curl http://localhost:8000/health
docker-compose exec frontend curl http://localhost:3000
```

### Access Container Shells
```bash
# Backend shell
docker-compose exec backend bash

# Frontend shell  
docker-compose exec frontend sh

# Database shell
docker-compose exec db psql -U postgres -d myapp
```

### Reset Everything
```bash
# Nuclear option - removes all containers and volumes
docker-compose down -v --remove-orphans
docker system prune -f
docker volume prune -f

# Then rebuild
docker-compose build --no-cache
docker-compose up -d
```

## Environment-Specific Issues

### macOS
- Ensure Docker Desktop has enough memory allocated (4GB+)
- Check file sharing permissions in Docker Desktop settings

### Windows
- Use WSL2 backend for better performance
- Ensure line endings are LF, not CRLF
- Run PowerShell/CMD as Administrator if needed

### Linux
- Add user to docker group: `sudo usermod -aG docker $USER`
- Restart shell after adding to docker group
- Check firewall settings for ports 3000, 8000, 5432

## Performance Issues

### Slow Build Times
```bash
# Use BuildKit for faster builds
export DOCKER_BUILDKIT=1
docker-compose build
```

### High Memory Usage
```bash
# Limit container memory in docker-compose.yml
services:
  backend:
    mem_limit: 512m
  frontend:
    mem_limit: 1g
```

## Getting Help

If you're still experiencing issues:

1. **Check the logs** with `docker-compose logs`
2. **Search existing issues** in the project repository
3. **Create a new issue** with:
   - Error message
   - Steps to reproduce
   - System information (OS, Docker version)
   - Relevant logs

## Quick Health Check

Run this command to verify everything is working:
```bash
# Check all services
curl http://localhost:8000/health  # Backend health
curl http://localhost:3000         # Frontend
docker-compose exec db pg_isready -U postgres  # Database
```

Expected responses:
- Backend: `{"status": "healthy"}`
- Frontend: HTML page
- Database: `postgres:5432 - accepting connections`
