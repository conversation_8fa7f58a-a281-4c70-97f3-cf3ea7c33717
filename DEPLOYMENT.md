# Deployment Guide

This guide covers deploying CW-Core to various environments.

## 🚀 Quick Deploy

### Local Development
```bash
# Clone and setup
git clone <your-repo>
cd <your-repo>
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### Using Make Commands
```bash
make dev-setup    # Build, start, and migrate
make up           # Start services
make down         # Stop services
make logs         # View logs
make test         # Run tests
```

## 🐳 Docker Deployment

### Development
```bash
docker-compose up -d
docker-compose exec backend alembic upgrade head
```

### Production
```bash
# Set environment variables
cp .env.example .env.prod
# Edit .env.prod with production values

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

## ☁️ Cloud Deployment

### AWS ECS/Fargate

1. **Build and push images to ECR**
```bash
# Build images
docker build -t your-app-frontend ./fe
docker build -t your-app-backend ./be

# Tag and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account>.dkr.ecr.us-east-1.amazonaws.com
docker tag your-app-frontend:latest <account>.dkr.ecr.us-east-1.amazonaws.com/your-app-frontend:latest
docker tag your-app-backend:latest <account>.dkr.ecr.us-east-1.amazonaws.com/your-app-backend:latest
docker push <account>.dkr.ecr.us-east-1.amazonaws.com/your-app-frontend:latest
docker push <account>.dkr.ecr.us-east-1.amazonaws.com/your-app-backend:latest
```

2. **Create ECS task definitions and services**
3. **Set up RDS PostgreSQL instance**
4. **Configure ALB for load balancing**

### Google Cloud Run

1. **Build and push to Container Registry**
```bash
# Configure gcloud
gcloud auth configure-docker

# Build and push
docker build -t gcr.io/your-project/frontend ./fe
docker build -t gcr.io/your-project/backend ./be
docker push gcr.io/your-project/frontend
docker push gcr.io/your-project/backend
```

2. **Deploy services**
```bash
# Deploy backend
gcloud run deploy backend \
  --image gcr.io/your-project/backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated

# Deploy frontend
gcloud run deploy frontend \
  --image gcr.io/your-project/frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Vercel + Railway

1. **Deploy frontend to Vercel**
```bash
npm i -g vercel
cd fe
vercel
```

2. **Deploy backend to Railway**
- Connect GitHub repo to Railway
- Set environment variables
- Deploy automatically

### DigitalOcean App Platform

1. **Create app.yaml**
```yaml
name: cw-core
services:
- name: frontend
  source_dir: /fe
  github:
    repo: your-username/your-repo
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  
- name: backend
  source_dir: /be
  github:
    repo: your-username/your-repo
    branch: main
  run_command: uvicorn app.main:app --host 0.0.0.0 --port 8080
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs

databases:
- name: postgres
  engine: PG
  version: "13"
```

## 🔧 Environment Variables

### Production Environment Variables

**Backend (.env)**
```env
DATABASE_URL=************************************/dbname
SECRET_KEY=your-super-secret-key-here
ENVIRONMENT=production
DEBUG=false
BACKEND_CORS_ORIGINS=["https://yourdomain.com"]
```

**Frontend (.env.local)**
```env
NEXT_PUBLIC_API_URL=https://your-backend-url.com
```

## 🔒 Security Considerations

### SSL/TLS
- Use HTTPS in production
- Configure SSL certificates
- Update CORS origins

### Database Security
- Use strong passwords
- Enable SSL connections
- Restrict network access
- Regular backups

### API Security
- Use strong JWT secrets
- Implement rate limiting
- Add request validation
- Monitor for suspicious activity

## 📊 Monitoring

### Health Checks
- Frontend: `GET /`
- Backend: `GET /health`
- Database: Connection test

### Logging
- Application logs
- Access logs
- Error tracking (Sentry)
- Performance monitoring

### Metrics
- Response times
- Error rates
- Database performance
- Resource usage

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run tests
        run: |
          docker-compose up -d
          docker-compose exec backend pytest
          docker-compose down

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to production
        run: |
          # Your deployment commands here
```

## 🆘 Troubleshooting

### Common Issues

1. **Database connection errors**
   - Check connection string
   - Verify database is running
   - Check network connectivity

2. **CORS errors**
   - Update BACKEND_CORS_ORIGINS
   - Check frontend URL configuration

3. **Build failures**
   - Check Docker logs
   - Verify dependencies
   - Check environment variables

### Debug Commands
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs backend
docker-compose logs frontend

# Access container shell
docker-compose exec backend bash
docker-compose exec frontend sh

# Check database
docker-compose exec db psql -U postgres -d myapp
```
