.PHONY: help build up down logs clean test migrate create-superuser

# Default target
help:
	@echo "Available commands:"
	@echo "  build          - Build all Docker images"
	@echo "  up             - Start all services in development mode"
	@echo "  down           - Stop all services"
	@echo "  logs           - Show logs from all services"
	@echo "  logs-fe        - Show frontend logs"
	@echo "  logs-be        - Show backend logs"
	@echo "  logs-db        - Show database logs"
	@echo "  clean          - Remove all containers and volumes"
	@echo "  test           - Run tests"
	@echo "  test-be        - Run backend tests"
	@echo "  migrate        - Run database migrations"
	@echo "  create-superuser - Create a superuser"
	@echo "  shell-be       - Open shell in backend container"
	@echo "  shell-fe       - Open shell in frontend container"
	@echo "  shell-db       - Open shell in database container"

# Build all images
build:
	docker-compose build

# Start services
up:
	docker-compose up -d

# Stop services
down:
	docker-compose down

# Show logs
logs:
	docker-compose logs -f

logs-fe:
	docker-compose logs -f frontend

logs-be:
	docker-compose logs -f backend

logs-db:
	docker-compose logs -f db

# Clean up
clean:
	docker-compose down -v --remove-orphans
	docker system prune -f

# Run tests
test: test-be

test-be:
	docker-compose exec backend pytest

# Database operations
migrate:
	docker-compose exec backend alembic upgrade head

create-migration:
	docker-compose exec backend alembic revision --autogenerate -m "$(name)"

create-superuser:
	docker-compose exec backend python scripts/create_superuser.py

# Shell access
shell-be:
	docker-compose exec backend bash

shell-fe:
	docker-compose exec frontend sh

shell-db:
	docker-compose exec db psql -U postgres -d myapp

# Development helpers
dev-setup: build up migrate
	@echo "Development environment is ready!"
	@echo "Frontend: http://localhost:3000"
	@echo "Backend: http://localhost:8000"
	@echo "API Docs: http://localhost:8000/docs"

# Production deployment
prod-build:
	docker-compose -f docker-compose.prod.yml build

prod-up:
	docker-compose -f docker-compose.prod.yml up -d

prod-down:
	docker-compose -f docker-compose.prod.yml down

prod-logs:
	docker-compose -f docker-compose.prod.yml logs -f
