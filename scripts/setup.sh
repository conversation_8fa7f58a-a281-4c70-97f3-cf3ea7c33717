#!/bin/bash

# CW-Core Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up CW-Core..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create environment files if they don't exist
echo "📝 Setting up environment files..."

if [ ! -f "be/.env" ]; then
    cp be/.env.example be/.env
    echo "✅ Created backend .env file"
fi

if [ ! -f "fe/.env.local" ]; then
    cp fe/.env.example fe/.env.local
    echo "✅ Created frontend .env.local file"
fi

# Build and start services
echo "🏗️  Building Docker images..."
if ! docker-compose build; then
    echo "❌ Failed to build Docker images. Please check the logs above."
    exit 1
fi

echo "🚀 Starting services..."
if ! docker-compose up -d; then
    echo "❌ Failed to start services. Please check the logs above."
    exit 1
fi

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 15

# Check if backend is healthy
echo "🔍 Checking backend health..."
for i in {1..30}; do
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ Backend is healthy!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Backend health check failed. Check logs with: docker-compose logs backend"
        exit 1
    fi
    sleep 2
done

# Run migrations
echo "🗃️  Running database migrations..."
if ! docker-compose exec backend alembic upgrade head; then
    echo "❌ Failed to run migrations. Check logs with: docker-compose logs backend"
    exit 1
fi

echo "✅ Setup complete!"
echo ""
echo "🌐 Your application is now running:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "📚 Useful commands:"
echo "   make logs          - View logs"
echo "   make down          - Stop services"
echo "   make create-superuser - Create admin user"
echo "   make test          - Run tests"
echo ""
echo "🎉 Happy coding!"
