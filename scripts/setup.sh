#!/bin/bash

# Full Stack App Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up Full Stack App..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create environment files if they don't exist
echo "📝 Setting up environment files..."

if [ ! -f "be/.env" ]; then
    cp be/.env.example be/.env
    echo "✅ Created backend .env file"
fi

if [ ! -f "fe/.env.local" ]; then
    cp fe/.env.example fe/.env.local
    echo "✅ Created frontend .env.local file"
fi

# Build and start services
echo "🏗️  Building Docker images..."
docker-compose build

echo "🚀 Starting services..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Run migrations
echo "🗃️  Running database migrations..."
docker-compose exec backend alembic upgrade head

echo "✅ Setup complete!"
echo ""
echo "🌐 Your application is now running:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "📚 Useful commands:"
echo "   make logs          - View logs"
echo "   make down          - Stop services"
echo "   make create-superuser - Create admin user"
echo "   make test          - Run tests"
echo ""
echo "🎉 Happy coding!"
