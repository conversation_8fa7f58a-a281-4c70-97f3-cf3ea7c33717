/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 22v-5", key: "sfixh4" }],
  ["path", { d: "M14 19v-2", key: "pdve8j" }],
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "M18 20v-3", key: "uox2gk" }],
  ["path", { d: "M2 13h20", key: "5evz65" }],
  ["path", { d: "M20 13V7l-5-5H6a2 2 0 0 0-2 2v9", key: "1rnpe2" }],
  ["path", { d: "M6 20v-3", key: "c6pdcb" }]
];
const Shredder = createLucideIcon("shredder", __iconNode);

export { __iconNode, Shredder as default };
//# sourceMappingURL=shredder.js.map
