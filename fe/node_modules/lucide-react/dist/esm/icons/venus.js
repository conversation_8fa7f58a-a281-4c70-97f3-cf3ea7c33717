/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 15v7", key: "t2xh3l" }],
  ["path", { d: "M9 19h6", key: "456am0" }],
  ["circle", { cx: "12", cy: "9", r: "6", key: "1nw4tq" }]
];
const Venus = createLucideIcon("venus", __iconNode);

export { __iconNode, Venus as default };
//# sourceMappingURL=venus.js.map
