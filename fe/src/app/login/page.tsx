'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { LoginForm } from '@/components/auth/LoginForm';
import { RegisterForm } from '@/components/auth/RegisterForm';
import { Button } from '@/components/ui/button';
import { LoginResponse, User } from '@/lib/api';

export default function LoginPage() {
  const [isLogin, setIsLogin] = useState(true);
  const [message, setMessage] = useState('');
  const router = useRouter();

  const handleLoginSuccess = (data: LoginResponse) => {
    setMessage(`Welcome back, ${data.user.username}!`);
    // Redirect to dashboard or home page
    setTimeout(() => {
      router.push('/');
    }, 1500);
  };

  const handleRegisterSuccess = (user: User) => {
    setMessage(`Account created successfully! Welcome, ${user.username}!`);
    // Switch to login form or redirect
    setTimeout(() => {
      setIsLogin(true);
      setMessage('');
    }, 2000);
  };

  const handleError = (error: string) => {
    setMessage(`Error: ${error}`);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            {isLogin ? 'Sign in to your account' : 'Create a new account'}
          </h2>
        </div>

        {message && (
          <div className={`text-center p-4 rounded-md ${
            message.includes('Error') 
              ? 'bg-red-50 text-red-700' 
              : 'bg-green-50 text-green-700'
          }`}>
            {message}
          </div>
        )}

        {isLogin ? (
          <LoginForm onSuccess={handleLoginSuccess} onError={handleError} />
        ) : (
          <RegisterForm onSuccess={handleRegisterSuccess} onError={handleError} />
        )}

        <div className="text-center">
          <Button
            variant="link"
            onClick={() => {
              setIsLogin(!isLogin);
              setMessage('');
            }}
          >
            {isLogin 
              ? "Don't have an account? Sign up" 
              : "Already have an account? Sign in"
            }
          </Button>
        </div>
      </div>
    </div>
  );
}
