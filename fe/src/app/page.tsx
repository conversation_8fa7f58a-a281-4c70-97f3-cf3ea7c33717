'use client';

import { useState, useEffect } from 'react';
import { apiClient, User } from '@/lib/api';

export default function Home() {
  const [healthStatus, setHealthStatus] = useState<string>('');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const checkHealth = async () => {
      try {
        const health = await apiClient.healthCheck();
        setHealthStatus(health.status);
      } catch (err) {
        setError('Failed to connect to backend');
        console.error('Health check failed:', err);
      }
    };

    checkHealth();
    setLoading(false);
  }, []);

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8">Full Stack App</h1>

        <div className="grid gap-6">
          {/* Health Status Card */}
          <div className="border rounded-lg p-6">
            <h2 className="text-2xl font-semibold mb-4">Backend Status</h2>
            {loading ? (
              <p className="text-gray-600">Checking connection...</p>
            ) : error ? (
              <p className="text-red-600">{error}</p>
            ) : (
              <p className="text-green-600">Backend is {healthStatus}</p>
            )}
          </div>

          {/* Features Card */}
          <div className="border rounded-lg p-6">
            <h2 className="text-2xl font-semibold mb-4">Features</h2>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Next.js 14 with TypeScript
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                shadcn/ui Design System
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                FastAPI Backend
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                PostgreSQL Database
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                SQLAlchemy ORM
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Docker Compose Setup
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Authentication System
              </li>
            </ul>
          </div>

          {/* Quick Start Card */}
          <div className="border rounded-lg p-6">
            <h2 className="text-2xl font-semibold mb-4">Quick Start</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold">1. Start the services:</h3>
                <code className="block bg-gray-100 p-2 rounded mt-1">
                  docker-compose up -d
                </code>
              </div>
              <div>
                <h3 className="font-semibold">2. Run database migrations:</h3>
                <code className="block bg-gray-100 p-2 rounded mt-1">
                  cd be && alembic upgrade head
                </code>
              </div>
              <div>
                <h3 className="font-semibold">3. Access the application:</h3>
                <ul className="mt-1 space-y-1">
                  <li>Frontend: <a href="http://localhost:3000" className="text-blue-600 hover:underline">http://localhost:3000</a></li>
                  <li>Backend API: <a href="http://localhost:8000" className="text-blue-600 hover:underline">http://localhost:8000</a></li>
                  <li>API Docs: <a href="http://localhost:8000/docs" className="text-blue-600 hover:underline">http://localhost:8000/docs</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
