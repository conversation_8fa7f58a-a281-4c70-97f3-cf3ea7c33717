# Full Stack Application

A modern full-stack application built with Next.js, FastAPI, and PostgreSQL.

## 🚀 Tech Stack

### Frontend (`fe/`)
- **Next.js 14** with TypeScript
- **shadcn/ui** design system
- **Tailwind CSS** for styling
- **React 19** with modern hooks

### Backend (`be/`)
- **FastAPI** with Python 3.11+
- **SQLAlchemy** ORM for database operations
- **PostgreSQL** database
- **Alembic** for database migrations
- **JWT** authentication
- **Pydantic** for data validation

### Infrastructure
- **Docker & Docker Compose** for containerization
- **Nginx** reverse proxy for production
- **PostgreSQL 15** database

## 📁 Project Structure

```
/
├── fe/                     # Next.js frontend
│   ├── src/
│   │   ├── app/           # App router pages
│   │   ├── lib/           # Utilities and API client
│   │   └── components/    # React components
│   ├── Dockerfile
│   └── package.json
├── be/                     # FastAPI backend
│   ├── app/
│   │   ├── api/           # API routes
│   │   ├── core/          # Core functionality
│   │   ├── crud/          # Database operations
│   │   ├── models/        # SQLAlchemy models
│   │   └── schemas/       # Pydantic schemas
│   ├── alembic/           # Database migrations
│   ├── Dockerfile
│   └── requirements.txt
├── docker-compose.yml      # Development setup
├── docker-compose.prod.yml # Production setup
└── README.md
```

## 🛠️ Quick Start

### Prerequisites
- Docker and Docker Compose
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd <your-repo-name>
   ```

2. **Start the services**
   ```bash
   docker-compose up -d
   ```

3. **Run database migrations**
   ```bash
   cd be
   docker-compose exec backend alembic upgrade head
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Database: localhost:5432

### Manual Setup (without Docker)

#### Backend Setup
```bash
cd be
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your database credentials

# Run migrations
alembic upgrade head

# Start the server
uvicorn app.main:app --reload
```

#### Frontend Setup
```bash
cd fe
npm install
npm run dev
```

## 🔧 Configuration

### Environment Variables

#### Backend (`.env`)
```env
DATABASE_URL=postgresql://postgres:password@localhost:5432/myapp
SECRET_KEY=your-secret-key-here
API_V1_STR=/api/v1
ACCESS_TOKEN_EXPIRE_MINUTES=30
BACKEND_CORS_ORIGINS=["http://localhost:3000"]
```

#### Frontend (`.env.local`)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## 📊 Database

The application uses PostgreSQL with SQLAlchemy ORM. Database migrations are managed with Alembic.

### Creating Migrations
```bash
cd be
alembic revision --autogenerate -m "Description of changes"
alembic upgrade head
```

### Database Schema
- **Users table**: Authentication and user management

## 🔐 Authentication

The application uses JWT-based authentication:
- Login endpoint: `POST /api/v1/auth/login`
- Protected routes require `Authorization: Bearer <token>` header
- Tokens expire after 30 minutes (configurable)

## 🚀 Production Deployment

### Using Docker Compose
```bash
# Set production environment variables
cp .env.example .env.prod
# Edit .env.prod with production values

# Deploy with production configuration
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Variables for Production
- Set `SECRET_KEY` to a secure random string
- Update `DATABASE_URL` with production database credentials
- Configure `BACKEND_CORS_ORIGINS` with your domain
- Set `NEXT_PUBLIC_API_URL` to your backend URL

## 🧪 Testing

### Backend Tests
```bash
cd be
pytest
```

### Frontend Tests
```bash
cd fe
npm test
```

## 📝 API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Interactive API docs with Swagger UI
- OpenAPI specification: http://localhost:8000/openapi.json

## 🔄 Development Workflow

1. **Backend changes**: The FastAPI server auto-reloads on file changes
2. **Frontend changes**: Next.js has hot reload enabled
3. **Database changes**: Create migrations with Alembic
4. **New dependencies**: 
   - Backend: Add to `requirements.txt` and rebuild container
   - Frontend: Use `npm install` and rebuild container

## 🐛 Troubleshooting

### Common Issues

1. **Database connection errors**
   - Ensure PostgreSQL is running
   - Check database credentials in `.env`
   - Verify database exists

2. **CORS errors**
   - Check `BACKEND_CORS_ORIGINS` in backend configuration
   - Ensure frontend URL is included in allowed origins

3. **Port conflicts**
   - Check if ports 3000, 8000, or 5432 are already in use
   - Modify port mappings in `docker-compose.yml`

### Logs
```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs backend
docker-compose logs frontend
docker-compose logs db
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
